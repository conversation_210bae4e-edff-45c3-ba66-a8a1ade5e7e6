<template>
  <div class="dev-tools">
    <div class="dev-header">
      <h1>🛠️ 开发工具</h1>
      <p>用于测试和调试各种功能模块的开发工具集合</p>
    </div>

    <div class="tools-grid">
      <!-- API测试工具 -->
      <div class="tool-category">
        <h2>📡 API测试工具</h2>
        <div class="tool-cards">
          <router-link to="/test/homework-api" class="tool-card">
            <div class="tool-icon">📝</div>
            <h3>作业API测试</h3>
            <p>测试作业相关的API接口功能</p>
            <div class="tool-status">✅ 可用</div>
          </router-link>
          
          <div class="tool-card disabled">
            <div class="tool-icon">📚</div>
            <h3>课程API测试</h3>
            <p>测试课程相关的API接口功能</p>
            <div class="tool-status">🚧 开发中</div>
          </div>
        </div>
      </div>

      <!-- 快速链接 -->
      <div class="tool-category">
        <h2>🔗 快速链接</h2>
        <div class="tool-cards">
          <a href="/" class="tool-card">
            <div class="tool-icon">🏠</div>
            <h3>返回首页</h3>
            <p>回到主页面</p>
          </a>
          
          <a href="/teacher" class="tool-card">
            <div class="tool-icon">👨‍🏫</div>
            <h3>教师端</h3>
            <p>进入教师工作台</p>
          </a>
        </div>
      </div>
    </div>

    <!-- 开发信息 -->
    <div class="dev-info">
      <h2>ℹ️ 开发信息</h2>
      <div class="info-grid">
        <div class="info-item">
          <strong>环境:</strong>
          <span>{{ isDevelopment ? '开发环境' : '生产环境' }}</span>
        </div>
        <div class="info-item">
          <strong>API地址:</strong>
          <span>{{ apiBaseUrl }}</span>
        </div>
        <div class="info-item">
          <strong>版本:</strong>
          <span>v1.0.0</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

// 计算属性
const isDevelopment = computed(() => {
  return import.meta.env.MODE === 'development';
});

const apiBaseUrl = computed(() => {
  return import.meta.env.VITE_API_BASE_URL || 'http://************:8080';
});
</script>

<style scoped>
.dev-tools {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.dev-header {
  text-align: center;
  margin-bottom: 3rem;
  color: white;
}

.dev-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.dev-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.tools-grid {
  display: grid;
  gap: 2rem;
  margin-bottom: 3rem;
}

.tool-category {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.tool-category h2 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.tool-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.tool-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 0.5rem;
  padding: 1.5rem;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.tool-card:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.95);
}

.tool-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.tool-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.tool-card h3 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
  font-size: 1.1rem;
}

.tool-card p {
  margin: 0 0 1rem 0;
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.4;
}

.tool-status {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  background: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.dev-info {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dev-info h2 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  background: rgba(255, 255, 255, 0.9);
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item strong {
  color: #1f2937;
}

.info-item span {
  color: #6b7280;
  font-family: monospace;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .dev-tools {
    padding: 1rem;
  }
  
  .dev-header h1 {
    font-size: 2rem;
  }
  
  .tool-cards {
    grid-template-columns: 1fr;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>
