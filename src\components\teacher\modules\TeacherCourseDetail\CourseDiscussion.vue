<template>
  <div class="course-discussion">
    <!-- 页面标题 -->
    <div class="section-header">
      <h2 class="section-title">讨论区</h2>
      <div class="section-actions">
        <button class="btn btn-primary" @click="createTopic">
          <i class="btn-icon plus-icon"></i>
          发起讨论
        </button>
        <button class="btn btn-secondary" @click="manageTopics">
          <i class="btn-icon manage-icon"></i>
          管理讨论
        </button>
      </div>
    </div>

    <!-- 讨论统计 -->
    <div class="discussion-stats">
      <div class="stat-card">
        <div class="stat-number">{{ discussionStats.totalTopics }}</div>
        <div class="stat-label">讨论话题</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ discussionStats.totalReplies }}</div>
        <div class="stat-label">回复数量</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ discussionStats.activeUsers }}</div>
        <div class="stat-label">活跃用户</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ discussionStats.todayPosts }}</div>
        <div class="stat-label">今日发帖</div>
      </div>
    </div>

    <!-- 会话列表部分 -->
    <div class="session-section">
      <div class="section-header">
        <h3 class="section-title">讨论会话</h3>
        <div class="section-actions">
          <button class="btn btn-primary btn-sm" @click="createNewSession">
            <i class="btn-icon plus-icon"></i>
            新建会话
          </button>
          <button class="btn btn-secondary btn-sm" @click="refreshSessionList">
            <i class="btn-icon refresh-icon"></i>
            刷新
          </button>
        </div>
      </div>

      <!-- 会话列表 -->
      <div v-if="sessionLoading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>正在加载会话列表...</p>
      </div>

      <div v-else-if="sessionList.length > 0" class="session-list">
        <div
          v-for="session in sessionList"
          :key="session.id"
          class="session-item"
          @click="viewSession(session)"
        >
          <div class="session-info">
            <h4 class="session-name">{{ session.name || `会话 #${session.id}` }}</h4>
            <p class="session-remark" v-if="session.remark">{{ session.remark }}</p>
            <div class="session-meta">
              <span class="session-time">创建时间: {{ formatTime(session.createTime) }}</span>
              <span class="session-creator" v-if="session.createBy">创建者: {{ session.createBy }}</span>
            </div>
          </div>
          <div class="session-actions">
            <button class="btn btn-text btn-sm" @click.stop="editSession(session)">
              编辑
            </button>
            <button class="btn btn-text btn-sm text-danger" @click.stop="deleteSessionItem(session)">
              删除
            </button>
          </div>
        </div>
      </div>

      <div v-else class="empty-state">
        <div class="empty-icon">💬</div>
        <h3 class="empty-title">暂无会话</h3>
        <p class="empty-description">创建您的第一个讨论会话</p>
        <button class="btn btn-primary" @click="createNewSession">
          <i class="plus-icon"></i>
          新建会话
        </button>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-tabs">
        <button
          v-for="tab in filterTabs"
          :key="tab.key"
          :class="['filter-tab', { active: activeFilter === tab.key }]"
          @click="setActiveFilter(tab.key)"
        >
          {{ tab.label }}
        </button>
      </div>
      <div class="search-box">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索讨论内容..."
          class="search-input"
          @input="handleSearch"
        >
        <i class="search-icon"></i>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <p class="loading-text">正在加载讨论列表...</p>
    </div>

    <!-- 讨论列表 -->
    <div v-else-if="filteredTopics.length > 0" class="discussion-list">
      <div
        v-for="topic in filteredTopics"
        :key="topic.id"
        class="discussion-item"
        :class="{ pinned: topic.isPinned, hot: topic.isHot }"
        @click="viewTopic(topic)"
      >
        <div class="topic-header">
          <div class="topic-info">
            <div class="topic-badges">
              <span v-if="topic.isPinned" class="badge pinned">置顶</span>
              <span v-if="topic.isHot" class="badge hot">热门</span>
              <span v-if="topic.hasNewReply" class="badge new">新回复</span>
            </div>
            <h3 class="topic-title">{{ topic.title }}</h3>
            <p class="topic-preview">{{ topic.preview }}</p>
          </div>
          <div class="topic-meta">
            <div class="author-info">
              <img :src="topic.author.avatar" :alt="topic.author.name" class="author-avatar">
              <div class="author-details">
                <span class="author-name">{{ topic.author.name }}</span>
                <span class="author-role">{{ topic.author.role === 'teacher' ? '教师' : '学生' }}</span>
              </div>
            </div>
            <div class="topic-stats">
              <span class="stat-item">
                <i class="reply-icon"></i>
                {{ topic.replyCount }}
              </span>
              <span class="stat-item">
                <i class="view-icon"></i>
                {{ topic.viewCount }}
              </span>
              <span class="stat-item">
                <i class="time-icon"></i>
                {{ formatTime(topic.lastReplyTime) }}
              </span>
            </div>
          </div>
        </div>

        <div class="topic-actions">
          <button class="action-btn" @click.stop="replyTopic(topic)">
            <i class="reply-icon"></i>
            回复
          </button>
          <button class="action-btn" @click.stop="pinTopic(topic)">
            <i class="pin-icon"></i>
            {{ topic.isPinned ? '取消置顶' : '置顶' }}
          </button>
          <button class="action-btn" @click.stop="editTopic(topic)">
            <i class="edit-icon"></i>
            编辑
          </button>
          <button class="action-btn danger" @click.stop="deleteTopic(topic.id)">
            <i class="delete-icon"></i>
            删除
          </button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">💬</div>
      <h3 class="empty-title">暂无讨论</h3>
      <p class="empty-description">开始发起您的第一个讨论话题吧</p>
      <button class="btn btn-primary" @click="createTopic">
        <i class="plus-icon"></i>
        发起讨论
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { getSessionList, getSessionListByCourse, createSession, deleteSession } from '@/api/session';

// 定义props
const props = defineProps({
  courseId: {
    type: [String, Number],
    required: true
  }
});

// 定义emits
const emit = defineEmits(['refresh']);

// 响应式数据
const loading = ref(false);
const discussionTopics = ref([]);
const searchQuery = ref('');
const activeFilter = ref('all');

// 会话列表数据
const sessionList = ref([]);
const sessionLoading = ref(false);

// 讨论统计
const discussionStats = ref({
  totalTopics: 0,
  totalReplies: 0,
  activeUsers: 0,
  todayPosts: 0
});

// 筛选标签
const filterTabs = [
  { key: 'all', label: '全部' },
  { key: 'pinned', label: '置顶' },
  { key: 'hot', label: '热门' },
  { key: 'new', label: '最新' },
  { key: 'my', label: '我的发帖' }
];

// 计算过滤后的话题
const filteredTopics = computed(() => {
  let topics = discussionTopics.value;

  // 按筛选条件过滤
  if (activeFilter.value === 'pinned') {
    topics = topics.filter(topic => topic.isPinned);
  } else if (activeFilter.value === 'hot') {
    topics = topics.filter(topic => topic.isHot);
  } else if (activeFilter.value === 'new') {
    topics = topics.filter(topic => topic.hasNewReply);
  } else if (activeFilter.value === 'my') {
    topics = topics.filter(topic => topic.author.role === 'teacher');
  }

  // 按搜索关键词过滤
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase();
    topics = topics.filter(topic =>
      topic.title.toLowerCase().includes(query) ||
      topic.preview.toLowerCase().includes(query)
    );
  }

  return topics;
});

// 加载会话列表
const loadSessionList = async () => {
  sessionLoading.value = true;
  try {
    console.log('正在加载课程会话列表，课程ID:', props.courseId);

    // 调用会话列表API
    const response = await getSessionListByCourse(props.courseId);
    console.log('会话列表API响应:', response);

    if (response && response.code === 0) {
      sessionList.value = response.rows || [];
      console.log('会话列表加载成功，数量:', sessionList.value.length);
    } else {
      console.warn('会话列表API返回异常:', response);
      sessionList.value = [];
    }
  } catch (error) {
    console.error('加载会话列表失败:', error);
    sessionList.value = [];
    // 可以在这里添加错误提示
  } finally {
    sessionLoading.value = false;
  }
};

// 加载讨论列表
const loadDiscussionTopics = async () => {
  loading.value = true;
  try {
    // TODO: 实际的API调用
    // const response = await getDiscussionTopics(props.courseId);
    // discussionTopics.value = response.data;

    // 模拟土木工程结构力学讨论数据
    discussionTopics.value = [
      {
        id: 1,
        title: '关于静定梁弯矩图绘制的疑问',
        preview: '在绘制简支梁弯矩图时，遇到集中力偶作用的情况，弯矩图应该如何处理？',
        author: {
          id: 101,
          name: '张同学',
          role: 'student',
          avatar: '/api/placeholder/32/32'
        },
        replyCount: 8,
        viewCount: 45,
        lastReplyTime: '2024-04-12 14:30',
        isPinned: false,
        isHot: true,
        hasNewReply: true
      },
      {
        id: 2,
        title: '结构力学学习方法分享',
        preview: '作为过来人，想和大家分享一些学习结构力学的心得体会和解题技巧...',
        author: {
          id: 1,
          name: '李教授',
          role: 'teacher',
          avatar: '/api/placeholder/32/32'
        },
        replyCount: 15,
        viewCount: 128,
        lastReplyTime: '2024-04-11 16:45',
        isPinned: true,
        isHot: true,
        hasNewReply: false
      },
      {
        id: 3,
        title: '桁架内力计算中的符号规定',
        preview: '在使用节点法计算桁架内力时，拉力和压力的符号应该如何规定？',
        author: {
          id: 102,
          name: '王同学',
          role: 'student',
          avatar: '/api/placeholder/32/32'
        },
        replyCount: 6,
        viewCount: 32,
        lastReplyTime: '2024-04-10 09:15',
        isPinned: false,
        isHot: false,
        hasNewReply: true
      },
      {
        id: 4,
        title: '虚功原理的物理意义讨论',
        preview: '虚功原理在结构力学中的应用很广泛，但其物理意义有时不太好理解...',
        author: {
          id: 103,
          name: '陈同学',
          role: 'student',
          avatar: '/api/placeholder/32/32'
        },
        replyCount: 12,
        viewCount: 67,
        lastReplyTime: '2024-04-09 20:22',
        isPinned: false,
        isHot: false,
        hasNewReply: false
      }
    ];

    // 计算统计数据
    discussionStats.value = {
      totalTopics: discussionTopics.value.length,
      totalReplies: discussionTopics.value.reduce((sum, topic) => sum + topic.replyCount, 0),
      activeUsers: new Set(discussionTopics.value.map(topic => topic.author.id)).size,
      todayPosts: discussionTopics.value.filter(topic =>
        new Date(topic.lastReplyTime).toDateString() === new Date().toDateString()
      ).length
    };
  } catch (error) {
    console.error('加载讨论列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 格式化时间
const formatTime = (timeString) => {
  const date = new Date(timeString);
  const now = new Date();
  const diffMs = now - date;
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffHours < 1) {
    return '刚刚';
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return date.toLocaleDateString('zh-CN');
  }
};

// 设置活跃筛选器
const setActiveFilter = (filterKey) => {
  activeFilter.value = filterKey;
};

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
};

// 发起讨论
const createTopic = () => {
  console.log('发起新讨论');
  // TODO: 实现发起讨论逻辑
};

// 管理讨论
const manageTopics = () => {
  console.log('管理讨论');
  // TODO: 实现管理讨论逻辑
};

// 查看话题
const viewTopic = (topic) => {
  console.log('查看话题:', topic);
  // TODO: 实现查看话题详情逻辑
};

// 回复话题
const replyTopic = (topic) => {
  console.log('回复话题:', topic);
  // TODO: 实现回复话题逻辑
};

// 置顶话题
const pinTopic = (topic) => {
  console.log('置顶话题:', topic);
  topic.isPinned = !topic.isPinned;
  // TODO: 实现置顶话题逻辑
};

// 编辑话题
const editTopic = (topic) => {
  console.log('编辑话题:', topic);
  // TODO: 实现编辑话题逻辑
};

// 删除话题
const deleteTopic = (topicId) => {
  console.log('删除话题:', topicId);
  // TODO: 实现删除话题逻辑
};

// 会话相关操作函数

// 创建新会话
const createNewSession = async () => {
  console.log('创建新会话');
  try {
    // 这里可以弹出对话框让用户输入会话名称和备注
    const sessionName = prompt('请输入会话名称:');
    if (!sessionName) return;

    const sessionData = {
      name: sessionName,
      remark: '',
      courseId: props.courseId
    };

    const response = await createSession(sessionData);
    if (response && response.code === 0) {
      console.log('创建会话成功');
      await loadSessionList(); // 重新加载会话列表
    } else {
      console.error('创建会话失败:', response);
      alert('创建会话失败，请重试');
    }
  } catch (error) {
    console.error('创建会话异常:', error);
    alert('创建会话失败，请重试');
  }
};

// 刷新会话列表
const refreshSessionList = async () => {
  console.log('刷新会话列表');
  await loadSessionList();
};

// 查看会话
const viewSession = (session) => {
  console.log('查看会话:', session);
  // TODO: 实现查看会话详情逻辑，可能跳转到会话详情页面
};

// 编辑会话
const editSession = (session) => {
  console.log('编辑会话:', session);
  // TODO: 实现编辑会话逻辑，弹出编辑对话框
};

// 删除会话
const deleteSessionItem = async (session) => {
  console.log('删除会话:', session);
  if (!confirm(`确定要删除会话"${session.name || session.id}"吗？`)) {
    return;
  }

  try {
    const response = await deleteSession(session.id);
    if (response && response.code === 0) {
      console.log('删除会话成功');
      await loadSessionList(); // 重新加载会话列表
    } else {
      console.error('删除会话失败:', response);
      alert('删除会话失败，请重试');
    }
  } catch (error) {
    console.error('删除会话异常:', error);
    alert('删除会话失败，请重试');
  }
};

// 刷新所有数据
const refreshAllData = async () => {
  await Promise.all([
    loadSessionList(),
    loadDiscussionTopics()
  ]);
};

// 组件挂载时加载数据
onMounted(() => {
  refreshAllData();
});
</script>

<style scoped>
/* 讨论区样式 */
.course-discussion {
  background-color: var(--background-color, #ffffff);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background-color: var(--background-color, #ffffff);
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color, #1f2937);
  margin: 0;
}

.section-actions {
  display: flex;
  gap: 0.75rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.btn-primary {
  background-color: var(--primary-color, #6366f1);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-color-dark, #4f46e5);
}

.btn-secondary {
  background-color: var(--background-color-secondary, #f3f4f6);
  color: var(--text-color, #374151);
  border-color: var(--border-color, #d1d5db);
}

.btn-secondary:hover {
  background-color: var(--background-color-tertiary, #e5e7eb);
}

/* 讨论统计 */
.discussion-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  padding: 1.5rem;
  background-color: var(--background-color-secondary, #f9fafb);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.stat-card {
  text-align: center;
  padding: 1rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color, #6366f1);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-color-secondary, #6b7280);
}

/* 筛选和搜索 */
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background-color: white;
}

.filter-tabs {
  display: flex;
  gap: 0.5rem;
}

.filter-tab {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.375rem;
  background-color: white;
  color: var(--text-color-secondary, #6b7280);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-tab:hover {
  background-color: var(--background-color-secondary, #f3f4f6);
}

.filter-tab.active {
  background-color: var(--primary-color, #6366f1);
  color: white;
  border-color: var(--primary-color, #6366f1);
}

.search-box {
  position: relative;
  width: 300px;
}

.search-input {
  width: 100%;
  padding: 0.5rem 2.5rem 0.5rem 1rem;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color, #6366f1);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1rem;
  height: 1rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%236b7280'%3E%3Cpath fill-rule='evenodd' d='M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z' clip-rule='evenodd' /%3E%3C/svg%3E");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-color-secondary, #6b7280);
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid var(--border-color, #e5e7eb);
  border-top: 2px solid var(--primary-color, #6366f1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 讨论列表 */
.discussion-list {
  padding: 1rem;
}

.discussion-item {
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  overflow: hidden;
  transition: all 0.2s;
  background-color: white;
  cursor: pointer;
}

.discussion-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.discussion-item.pinned {
  border-color: var(--primary-color, #6366f1);
  background-color: rgba(99, 102, 241, 0.02);
}

.discussion-item.hot {
  border-color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.02);
}

.topic-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.topic-info {
  flex: 1;
  margin-right: 1rem;
}

.topic-badges {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.badge {
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge.pinned {
  background-color: #dbeafe;
  color: #1e40af;
}

.badge.hot {
  background-color: #fef3c7;
  color: #92400e;
}

.badge.new {
  background-color: #dcfce7;
  color: #166534;
}

.topic-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color, #1f2937);
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.topic-preview {
  color: var(--text-color-secondary, #6b7280);
  margin: 0;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.topic-meta {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  min-width: 200px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.author-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  object-fit: cover;
}

.author-details {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color, #1f2937);
}

.author-role {
  font-size: 0.75rem;
  color: var(--text-color-secondary, #6b7280);
}

.topic-stats {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: var(--text-color-secondary, #6b7280);
}

.topic-actions {
  display: flex;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: var(--background-color-secondary, #f9fafb);
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.375rem 0.75rem;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.25rem;
  background-color: white;
  color: var(--text-color, #374151);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  background-color: var(--background-color-secondary, #f3f4f6);
}

.action-btn.danger {
  color: #dc2626;
  border-color: #fecaca;
}

.action-btn.danger:hover {
  background-color: #fef2f2;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color, #1f2937);
  margin: 0 0 0.5rem 0;
}

.empty-description {
  color: var(--text-color-secondary, #6b7280);
  margin: 0 0 1.5rem 0;
}

/* 图标样式 */
.btn-icon, .plus-icon, .manage-icon, .reply-icon, .view-icon, .time-icon,
.pin-icon, .edit-icon, .delete-icon {
  width: 1rem;
  height: 1rem;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.plus-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.manage-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.reply-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M7.707 3.293a1 1 0 010 1.414L5.414 7H11a7 7 0 017 7v2a1 1 0 11-2 0v-2a5 5 0 00-5-5H5.414l2.293 2.293a1 1 0 11-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.view-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M10 12a2 2 0 100-4 2 2 0 000 4z' /%3E%3Cpath fill-rule='evenodd' d='M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.time-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.pin-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z' /%3E%3C/svg%3E");
}

.edit-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z' /%3E%3C/svg%3E");
}

.delete-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

/* 响应式设计 */
@media (max-width: 768px) {
  .discussion-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .filter-section {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .search-box {
    width: 100%;
  }

  .topic-header {
    flex-direction: column;
    gap: 1rem;
  }

  .topic-meta {
    min-width: auto;
    flex-direction: row;
    justify-content: space-between;
  }

  .topic-stats {
    flex-direction: row;
    gap: 1rem;
  }

  .topic-actions {
    justify-content: center;
  }
}

/* 会话列表样式 */
.session-section {
  margin-top: 2rem;
  background-color: var(--background-color, #ffffff);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.session-section .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background-color: var(--header-background, #f9fafb);
}

.session-section .section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
  margin: 0;
}

.session-section .section-actions {
  display: flex;
  gap: 0.5rem;
}

.session-list {
  padding: 1rem;
}

.session-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.5rem;
  margin-bottom: 0.75rem;
  background-color: var(--background-color, #ffffff);
  cursor: pointer;
  transition: all 0.2s ease;
}

.session-item:hover {
  border-color: var(--primary-color, #3b82f6);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.session-item:last-child {
  margin-bottom: 0;
}

.session-info {
  flex: 1;
}

.session-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
  margin: 0 0 0.25rem 0;
}

.session-remark {
  font-size: 0.875rem;
  color: var(--text-secondary, #6b7280);
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.session-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: var(--text-tertiary, #9ca3af);
}

.session-actions {
  display: flex;
  gap: 0.5rem;
}

.session-actions .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.text-danger {
  color: var(--danger-color, #ef4444) !important;
}

.text-danger:hover {
  color: var(--danger-hover, #dc2626) !important;
}

/* 刷新图标 */
.refresh-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary, #6b7280);
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid var(--border-color, #e5e7eb);
  border-top: 2px solid var(--primary-color, #3b82f6);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 - 会话列表 */
@media (max-width: 768px) {
  .session-section .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .session-item {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .session-actions {
    justify-content: flex-end;
  }

  .session-meta {
    flex-direction: column;
    gap: 0.25rem;
  }
}
</style>