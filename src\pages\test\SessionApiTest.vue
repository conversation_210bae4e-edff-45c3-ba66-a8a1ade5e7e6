<template>
  <div class="session-api-test">
    <div class="container">
      <h1>会话API测试页面</h1>
      
      <!-- 测试控制面板 -->
      <div class="test-panel">
        <h2>测试控制</h2>
        <div class="form-group">
          <label>课程ID:</label>
          <input v-model="testCourseId" type="number" placeholder="输入课程ID" />
        </div>
        
        <div class="button-group">
          <button @click="testGetSessionList" :disabled="loading">
            {{ loading ? '加载中...' : '获取会话列表' }}
          </button>
          <button @click="testCreateSession" :disabled="loading">
            创建测试会话
          </button>
          <button @click="clearResults">清空结果</button>
        </div>
      </div>

      <!-- 测试结果显示 -->
      <div class="results-panel">
        <h2>测试结果</h2>
        
        <!-- API响应信息 -->
        <div v-if="lastApiResponse" class="api-response">
          <h3>最新API响应:</h3>
          <div class="response-info">
            <div class="response-status" :class="lastApiResponse.success ? 'success' : 'error'">
              状态: {{ lastApiResponse.success ? '成功' : '失败' }}
            </div>
            <div class="response-time">
              响应时间: {{ lastApiResponse.timestamp }}
            </div>
          </div>
          <pre class="response-data">{{ JSON.stringify(lastApiResponse.data, null, 2) }}</pre>
        </div>

        <!-- 会话列表 -->
        <div v-if="sessionList.length > 0" class="session-list">
          <h3>会话列表 ({{ sessionList.length }} 条):</h3>
          <div class="session-items">
            <div v-for="session in sessionList" :key="session.id" class="session-item">
              <div class="session-info">
                <strong>ID: {{ session.id }}</strong>
                <div>名称: {{ session.name || '未命名' }}</div>
                <div v-if="session.remark">备注: {{ session.remark }}</div>
                <div class="session-meta">
                  <span>创建时间: {{ session.createTime || '未知' }}</span>
                  <span v-if="session.createBy">创建者: {{ session.createBy }}</span>
                </div>
              </div>
              <div class="session-actions">
                <button @click="testDeleteSession(session.id)" class="delete-btn">删除</button>
              </div>
            </div>
          </div>
        </div>

        <!-- 错误信息 -->
        <div v-if="errorMessage" class="error-message">
          <h3>错误信息:</h3>
          <p>{{ errorMessage }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { getSessionList, getSessionListByCourse, createSession, deleteSession } from '@/api/session';

// 响应式数据
const loading = ref(false);
const testCourseId = ref(1); // 默认课程ID
const sessionList = ref([]);
const lastApiResponse = ref(null);
const errorMessage = ref('');

// 测试获取会话列表
const testGetSessionList = async () => {
  loading.value = true;
  errorMessage.value = '';
  
  try {
    console.log('开始测试获取会话列表，课程ID:', testCourseId.value);
    
    const startTime = Date.now();
    const response = await getSessionListByCourse(testCourseId.value);
    const endTime = Date.now();
    
    console.log('会话列表API响应:', response);
    
    // 记录API响应
    lastApiResponse.value = {
      success: response && response.code === 0,
      timestamp: new Date().toLocaleString(),
      responseTime: `${endTime - startTime}ms`,
      data: response
    };
    
    if (response && response.code === 0) {
      sessionList.value = response.rows || [];
      console.log('获取会话列表成功，数量:', sessionList.value.length);
    } else {
      errorMessage.value = `API返回错误: ${response?.msg || '未知错误'}`;
      sessionList.value = [];
    }
  } catch (error) {
    console.error('获取会话列表失败:', error);
    errorMessage.value = `请求失败: ${error.message}`;
    lastApiResponse.value = {
      success: false,
      timestamp: new Date().toLocaleString(),
      data: { error: error.message }
    };
    sessionList.value = [];
  } finally {
    loading.value = false;
  }
};

// 测试创建会话
const testCreateSession = async () => {
  loading.value = true;
  errorMessage.value = '';
  
  try {
    const sessionData = {
      name: `测试会话_${Date.now()}`,
      remark: '这是一个API测试创建的会话',
      courseId: testCourseId.value
    };
    
    console.log('开始测试创建会话:', sessionData);
    
    const startTime = Date.now();
    const response = await createSession(sessionData);
    const endTime = Date.now();
    
    console.log('创建会话API响应:', response);
    
    // 记录API响应
    lastApiResponse.value = {
      success: response && response.code === 0,
      timestamp: new Date().toLocaleString(),
      responseTime: `${endTime - startTime}ms`,
      data: response
    };
    
    if (response && response.code === 0) {
      console.log('创建会话成功');
      // 重新获取会话列表
      await testGetSessionList();
    } else {
      errorMessage.value = `创建会话失败: ${response?.msg || '未知错误'}`;
    }
  } catch (error) {
    console.error('创建会话失败:', error);
    errorMessage.value = `创建会话请求失败: ${error.message}`;
    lastApiResponse.value = {
      success: false,
      timestamp: new Date().toLocaleString(),
      data: { error: error.message }
    };
  } finally {
    loading.value = false;
  }
};

// 测试删除会话
const testDeleteSession = async (sessionId) => {
  if (!confirm(`确定要删除会话 ${sessionId} 吗？`)) {
    return;
  }
  
  loading.value = true;
  errorMessage.value = '';
  
  try {
    console.log('开始测试删除会话:', sessionId);
    
    const startTime = Date.now();
    const response = await deleteSession(sessionId);
    const endTime = Date.now();
    
    console.log('删除会话API响应:', response);
    
    // 记录API响应
    lastApiResponse.value = {
      success: response && response.code === 0,
      timestamp: new Date().toLocaleString(),
      responseTime: `${endTime - startTime}ms`,
      data: response
    };
    
    if (response && response.code === 0) {
      console.log('删除会话成功');
      // 重新获取会话列表
      await testGetSessionList();
    } else {
      errorMessage.value = `删除会话失败: ${response?.msg || '未知错误'}`;
    }
  } catch (error) {
    console.error('删除会话失败:', error);
    errorMessage.value = `删除会话请求失败: ${error.message}`;
    lastApiResponse.value = {
      success: false,
      timestamp: new Date().toLocaleString(),
      data: { error: error.message }
    };
  } finally {
    loading.value = false;
  }
};

// 清空结果
const clearResults = () => {
  sessionList.value = [];
  lastApiResponse.value = null;
  errorMessage.value = '';
  console.log('已清空测试结果');
};
</script>

<style scoped>
.session-api-test {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 2rem;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 2rem;
}

.test-panel, .results-panel {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.form-group input {
  width: 200px;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.button-group {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
}

button:not(.delete-btn) {
  background-color: #007bff;
  color: white;
}

button:not(.delete-btn):hover {
  background-color: #0056b3;
}

button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.delete-btn {
  background-color: #dc3545;
  color: white;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.delete-btn:hover {
  background-color: #c82333;
}

.api-response {
  margin-bottom: 2rem;
}

.response-info {
  display: flex;
  gap: 2rem;
  margin-bottom: 1rem;
}

.response-status.success {
  color: #28a745;
  font-weight: 600;
}

.response-status.error {
  color: #dc3545;
  font-weight: 600;
}

.response-data {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 1rem;
  overflow-x: auto;
  font-size: 0.875rem;
}

.session-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.session-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  background-color: #f8f9fa;
}

.session-info {
  flex: 1;
}

.session-meta {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.session-meta span {
  margin-right: 1rem;
}

.error-message {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 1rem;
  color: #721c24;
}
</style>
